package com.socialplay.gpark.ui.gamedetail.sendflower

import android.animation.ValueAnimator
import android.content.DialogInterface
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResult
import androidx.lifecycle.LifecycleCoroutineScope
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.GridLayoutManager
import androidx.transition.Fade
import androidx.transition.Slide
import androidx.transition.Transition
import androidx.transition.TransitionManager
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.model.gift.GiftFlower
import com.socialplay.gpark.data.model.gift.SendGiftData
import com.socialplay.gpark.databinding.DialogSendFlowerBinding
import com.socialplay.gpark.databinding.ItemFlowSendFlowersBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseRVBottomSheetDialogFragment
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.gamepay.PayInfo
import com.socialplay.gpark.ui.gamepay.PayResult
import com.socialplay.gpark.util.UnitUtilWrapper
import com.socialplay.gpark.util.extension.addUpdateListener
import com.socialplay.gpark.util.extension.enableAllWithAlpha
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.toStringOrEmpty
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext
import java.util.concurrent.ConcurrentLinkedDeque

/**
 * TODO 继承自BottomSheetDialogFragment的Fragment会在每次onResume的时候执行入场动画
 */
class SendFlowerDialog : BaseRVBottomSheetDialogFragment() {
    override val binding: DialogSendFlowerBinding by viewBinding(DialogSendFlowerBinding::inflate)
    private val viewModel: SendFlowerViewModel by fragmentViewModel()

    private val args by navArgs<SendFlowerDialogArgs>()

    private var giftMessagesDisplayManager: GiftMessageDisplayManager? = null

    /**
     * 送花是否正在网络请求中
     */
    private var isSendFlowerRequest: Boolean = false

    private var sendFlowerCount = 0

    /**
     * 送花按钮是否被点击过
     */
    private var shouldRefreshBalance = false

    private var myAvatar: String? = null

    override val recyclerView: EpoxyRecyclerView
        get() = binding.rvGifts

    companion object {
        const val REQUEST_KEY_SEND_FLOWER_COUNT = "request_key_send_flower_count"
        const val KEY_SEND_FLOWER_COUNT = "key_send_flower_count"
        fun showDialog(
            fragment: Fragment,
            gameId: String,
            gameType: Int,
            source: String,
            hasReceivedGift: Boolean,
        ) {
            Analytics.track(
                EventConstants.C_GAMEDETAIL_FLOWER_PAGE_SHOW,
                "gameid" to gameId,
                "creatortype" to gameType.toString(),
                "source" to source,
            )
            SendFlowerDialog().apply {
                arguments = SendFlowerDialogArgs(
                    gameId = gameId,
                    gameType = gameType,
                    source = source,
                    hasReceivedGift = hasReceivedGift,
                ).toBundle()
            }.show(fragment.childFragmentManager, "SendFlowerDialog")
        }
    }

    override var heightPercent = 1F

    private val itemClickedListener = object : IFlowerGiftItemListener {
        override fun onClicked(
            gift: GiftFlower,
            isCustomize: Boolean,
            isSelected: Boolean
        ) {
            if (!isSelected) {
                if (isCustomize) {
                    MetaRouter.GameDetail.showCustomizeFlowerCountDialog(this@SendFlowerDialog) { count ->
                        Analytics.track(
                            EventConstants.C_FLOWER_PAGE_SEND_CLICK,
                            "gameid" to args.gameId,
                            "creatortype" to args.gameType.toString(),
                            "source" to args.source,
                        )
                        // 自定义送花, 直接触发送花流程, 不用先框选
//                        viewModel.updateSelectedGift(gift, count)
                        shouldRefreshBalance = true
                        sendFlower(gift, count, viewModel.getGiftPrice(gift, count))
                    }
                } else {
                    viewModel.updateSelectedGift(gift)
                }
            }
        }
    }

    override fun epoxyController(): EpoxyController = simpleController(
        viewModel,
        SendFlowerState::giftFlowers,
        SendFlowerState::selectedGift,
        SendFlowerState::customizeFlowerGiftProductId,
    ) { giftFlowersAsync, selectedGift, customizeFlowerGiftProductId ->
        val giftFlowers = giftFlowersAsync.invoke()
        if (giftFlowers.isNullOrEmpty()) {
            // TODO 获取送礼列表失败时
        } else {
            val customizeProductId = customizeFlowerGiftProductId.invoke()
            var customizeGiftInfoList: MutableList<GiftFlower> = mutableListOf()
            giftFlowers.forEach { gift ->
                if (customizeProductId != null && customizeProductId == gift.id) {
                    customizeGiftInfoList.add(gift)
                }
                add {
                    SendFlowerGiftItem(
                        glide,
                        gift,
                        false,
                        selectedGift?.id == gift.id,
                        itemClickedListener
                    ).id("SendFlowerGiftItem-${gift.id}")
                }
            }
            customizeGiftInfoList.forEach { gift ->
                add {
                    SendFlowerGiftItem(
                        glide,
                        gift,
                        true,
                        false,
                        itemClickedListener
                    ).id("SendFlowerGiftItem-customize-${gift.id}")
                }
            }
            if (selectedGift == null) {
                // 默认选中第一项
                viewModel.updateSelectedGift(giftFlowers.first())
            }
        }
    }

    override fun init() {
        skipCollapsed()
        enableDrag(false)
        enableHide(false)
        binding.viewTop.setOnAntiViolenceClickListener {
            dismissAllowingStateLoss()
        }

        binding.rvGifts.layoutManager = GridLayoutManager(requireContext(), 3)

        binding.tvCoinsBalance.setOnAntiViolenceClickListener {
            shouldRefreshBalance = true
            MetaRouter.Pay.goBuyCoinsPage(
                requireContext(),
                this,
                getPageName(),
            )
        }
        binding.vTitleClick.setOnAntiViolenceClickListener {
            val h5PageConfigInteractor: H5PageConfigInteractor =
                GlobalContext.get().get<H5PageConfigInteractor>()
            val item =
                h5PageConfigInteractor.getH5PageConfigItem(H5PageConfigInteractor.SEND_FLOWER_README_URL)
            MetaRouter.Web.navigate(
                this,
                title = item.title,
                url = item.url,
            )
        }
        binding.tvNoFlowersSendDesc.visible(!args.hasReceivedGift)

        binding.tvSendFlowerBtn.setOnClickListener {
            if (isSendFlowerRequest) {
                return@setOnClickListener
            }
            Analytics.track(
                EventConstants.C_FLOWER_PAGE_SEND_CLICK,
                "gameid" to args.gameId,
                "creatortype" to args.gameType.toString(),
                "source" to args.source,
            )
            val viewModelState = viewModel.oldState
            val selectedGift = viewModelState.selectedGift ?: return@setOnClickListener
            val giftCount = viewModelState.giftCount
            val requiresCoins = viewModelState.requiresCoins
            shouldRefreshBalance = true
            sendFlower(selectedGift, giftCount, requiresCoins)
        }

        val recyclerFlows = listOf(
            binding.flow0,
            binding.flow1,
            binding.flow2,
        )
        giftMessagesDisplayManager = GiftMessageDisplayManager(
            recyclerFlows,
            glide!!,
            lifecycleScope,
            viewLifecycleOwner
        )

        viewModel.onEach(SendFlowerState::requiresCoins) { requiresCoins ->
            binding.tvRequiresCoins.text = UnitUtilWrapper.formatCoinCont(requiresCoins)
            binding.tvSendFlowerBtn.enableAllWithAlpha(requiresCoins > 0)
        }

        viewModel.onEach(SendFlowerState::coinsBalance) { coinsBalance ->
            if (coinsBalance == null) {
                binding.tvCoinsBalance.text = "---"
            } else {
                binding.tvCoinsBalance.text = UnitUtilWrapper.formatCoinCont(coinsBalance)
            }
        }
        viewModel.onAsync(
            SendFlowerState::sendFlowerUserList,
            onFail = {
                giftMessagesDisplayManager?.clean()
                giftMessagesDisplayManager?.enableLoopShow = false
            }
        ) { userList ->
            // 送礼数量大于3条的时候, 就滚动播报
            giftMessagesDisplayManager?.clean()
            giftMessagesDisplayManager?.enableLoopShow = userList.size > 3
            giftMessagesDisplayManager?.addInfos(userList)
            giftMessagesDisplayManager?.startShow()
        }
        viewModel.accountInteractor.accountLiveData.observe(viewLifecycleOwner) {
            myAvatar = it?.portrait
        }
        viewModel.loadCustomizeFlowerGiftProductId()
        viewModel.loadFlowerGifts()
        viewModel.loadBalance()
        viewModel.loadSendFlowerUserList(args.gameId)
    }

    fun sendFlower(
        gift: GiftFlower,
        giftCount: Int,
        requiresCoins: Long
    ) {
        if (!isBindingAvailable()) {
            return
        }
        isSendFlowerRequest = true
        MetaRouter.Pay.startPay(
            PayInfo(
                fromMW = false,
                showConfirm = false,
                autoConfirmAfterRecharge = false,
                gameId = args.gameId,
                productCode = (gift.id ?: "").toString(),
                productName = (gift.name ?: "").toString(),
                productPrice = gift.getSingleGiftPrice(),
                productCount = giftCount,
                sceneCode = gift.sceneCode ?: 0,
                payAmount = requiresCoins,
                gameType = args.gameType,
                source = "flower",
                onPayResult = { payResult ->
                    if (payResult.isSuccess) {
                        Analytics.track(
                            EventConstants.C_FLOWER_PAGE_PAY_SUCCESS,
                            "gameid" to args.gameId,
                            "creatortype" to args.gameType.toString(),
                            "source" to args.source,
                            "orderid" to payResult.orderId.orEmpty(),
                            "commodityid" to gift.id.toStringOrEmpty(),
                            "propprice" to requiresCoins,
                            "scenecode" to (gift.sceneCode ?: 0).toString(),
                        )
                    } else if (payResult.resultCode == PayResult.RESULT_CODE_FAILED) {
                        Analytics.track(
                            EventConstants.C_FLOWER_PAGE_PAY_FAIL,
                            "gameid" to args.gameId,
                            "creatortype" to args.gameType.toString(),
                            "source" to args.source,
                            "orderid" to payResult.orderId.orEmpty(),
                            "commodityid" to gift.id.toStringOrEmpty(),
                            "propprice" to requiresCoins,
                            "scenecode" to (gift.sceneCode ?: 0).toString(),
                            "failedcode" to payResult.failedCode.toStringOrEmpty(),
                            "failedmsg" to (payResult.failedReason ?: ""),
                        )
                    }
                    lifecycleScope.launch(Dispatchers.Main) {
                        if (!isBindingAvailable()) {
                            return@launch
                        }
                        if (payResult.isSuccess) {
                            val count = (gift.baseNum ?: 0) * giftCount
                            sendFlowerCount += count
                            giftMessagesDisplayManager?.showMime(
                                SendGiftData(
                                    uid = "",
                                    nickname = getString(R.string.send_flower_dialog_sender_you),
                                    portrait = myAvatar ?: "",
                                    tippingCnt = count.toString(),
                                    isMe = true,
                                )
                            )
                        } else if (payResult.resultCode == PayResult.RESULT_CODE_FAILED) {
                            // 送花失败
                            toast(R.string.toast_send_flower_failed)
                        }
                        isSendFlowerRequest = false
                        val coinsBalance = payResult.coinsBalance
                        if (coinsBalance != null) {
                            viewModel.updateCoinsBalance(coinsBalance)
                        } else {
                            if (payResult.resultCode == PayResult.RESULT_CODE_SUCCESS
                                || payResult.resultCode == PayResult.RESULT_CODE_AUTO_CANCEL
                            ) {
                                viewModel.loadBalance()
                            }
                        }
                    }
                },
            )
        )
    }

    override fun onResume() {
        super.onResume()
        // 如果用户点击过送花
        if (shouldRefreshBalance) {
            shouldRefreshBalance = false
            viewModel.loadBalance()
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        setFragmentResult(REQUEST_KEY_SEND_FLOWER_COUNT, Bundle().apply {
            putInt(KEY_SEND_FLOWER_COUNT, sendFlowerCount)
        })
    }

    override fun needCountTime(): Boolean {
        return false
    }

    override fun getPageName(): String = PageNameConstants.DIALOG_SEND_FLOWER
}

private class GiftMessageDisplayManager(
    val flows: List<ItemFlowSendFlowersBinding>,
    val glide: RequestManager,
    val coroutineScope: LifecycleCoroutineScope,
    val lifecycleOwner: LifecycleOwner,
) {
    /**
     * 其他人送的礼物后显示
     */
    private val infoQueue = ConcurrentLinkedDeque<SendGiftData>()

    /**
     * 当前用户刚送出的礼物, 应该优先显示
     */
    private val mimeInfoQueue = ConcurrentLinkedDeque<SendGiftData>()
    private var messageItems = listOf<GiftMessageItem>()

    /**
     * 其他人送花的信息是否可以循环播报
     */
    var enableLoopShow = false

    init {
        messageItems = flows.map { flow ->
            GiftMessageItem(
                flow,
                glide,
                coroutineScope,
                lifecycleOwner,
                onShow = {
                    mimeInfoQueue.poll()
                }, onHide = { flow, sendGiftData ->
                    if (enableLoopShow && sendGiftData != null && !sendGiftData.isMe) {
                        infoQueue.offer(sendGiftData)
                    }
                    if (flow.haveNextShowInfo()) {
                        return@GiftMessageItem
                    }
                    val mimeInfo = mimeInfoQueue.poll()
                    if (mimeInfo == null) {
                        val info = infoQueue.poll()
                        if (info != null) {
                            flow.show(info)
                        }
                    } else {
                        flow.show(mimeInfo)
                    }
                })
        }
    }

    fun addInfos(list: List<SendGiftData>) {
        infoQueue.addAll(list)
    }

    fun clean() {
        messageItems.forEach { item -> item.reset() }
        infoQueue.clear()
        mimeInfoQueue.clear()
    }

    fun showMime(info: SendGiftData) {
        if (mimeInfoQueue.isNotEmpty()) {
            mimeInfoQueue.offer(info)
            return
        }
        // 寻找一个可用的来展示
        var targetIndex = -1
        var targetShowTime = Long.MAX_VALUE
        for (i in 0..messageItems.size - 1) {
            val flow = messageItems[i]
            val status = flow.currentStatus()
            if (status == GiftMessageItem.STATUS_INACTIVE) {
                targetIndex = i
                targetShowTime = -1L
                break
            } else if ((status == GiftMessageItem.STATUS_SHOW || status == GiftMessageItem.STATUS_HIDE)
                && flow.currentShowStartTimestamp() < targetShowTime
                && !flow.haveNextShowInfo()
            ) {
                targetIndex = i
                targetShowTime = flow.currentShowStartTimestamp()
            }
        }
        if (targetIndex >= 0) {
            if (targetShowTime < 0) {
                messageItems[targetIndex].show(info)
                return
            } else if (messageItems[targetIndex].currentStatus() == GiftMessageItem.STATUS_SHOW) {
                messageItems[targetIndex].hideWithNextInfo(info)
                return
            }
        }
        mimeInfoQueue.offer(info)
    }

    fun startShow() {
        messageItems.forEachIndexed { position, flow ->
            coroutineScope.launch(Dispatchers.Main) {
                delay(700L * position)
                if (flow.currentStatus() == GiftMessageItem.STATUS_INACTIVE) {
                    val info = infoQueue.poll()
                    if (info != null) {
                        flow.show(info)
                    }
                }
            }
        }
    }
}

private data class GiftMessageItem(
    val flow: ItemFlowSendFlowersBinding,
    val glide: RequestManager,
    val coroutineScope: LifecycleCoroutineScope,
    val lifecycleOwner: LifecycleOwner,
    val onShow: () -> SendGiftData?,
    val onHide: (GiftMessageItem, SendGiftData?) -> Unit,
) {
    companion object {
        const val STATUS_ENTER = 1
        const val STATUS_COUNT_SCALE_UP = 2
        const val STATUS_COUNT_SCALE_DOWN = 3
        const val STATUS_SHOW = 4
        const val STATUS_HIDE = 5
        const val STATUS_INACTIVE = 6

        const val ENTER_DURATION = 300L
        const val COUNT_SCALE_UP_DURATION = 200L
        const val COUNT_SCALE_DOWN_DURATION = 200L
        const val SHOW_DURATION = 2000L
        const val HIDE_DURATION = 300L
    }

    private var status = STATUS_INACTIVE
    private var showStartTimestamp = 0L
    private var mCurrentShowInfo: SendGiftData? = null
    private var mNextShowInfo: SendGiftData? = null

    fun reset() {
        status = STATUS_INACTIVE
        showStartTimestamp = 0
        mCurrentShowInfo = null
        mNextShowInfo = null
    }

    fun currentStatus(): Int {
        return status
    }

    fun currentShowStartTimestamp(): Long {
        return showStartTimestamp
    }

    fun haveNextShowInfo(): Boolean {
        return mNextShowInfo != null
    }

    fun show(message: SendGiftData) {
//        if (status != STATUS_INACTIVE) {
//            return
//        }
        mCurrentShowInfo = message
        status = STATUS_ENTER
        bindingItem(flow, message)
        coroutineScope.launch(Dispatchers.Main) {
            enter(flow)
            status = STATUS_COUNT_SCALE_UP
            scaleUpCount(flow)
            status = STATUS_COUNT_SCALE_DOWN
            scaleDownCount(flow)

            status = STATUS_SHOW
            showStartTimestamp = System.currentTimeMillis()
            val nextShowInfo = onShow()
            if (nextShowInfo == null) {
                delay(SHOW_DURATION)
                // 可能外部调用了hide()方法之后导致状态变化了, 这里就无需重复做hide了
                if (status == STATUS_SHOW && mCurrentShowInfo === message) {
                    status = STATUS_HIDE
                    hide(flow)
                    status = STATUS_INACTIVE

                    onHide(this@GiftMessageItem, message)
                }
            } else {
                hideWithNextInfo(nextShowInfo)
            }
        }
    }

    fun hideWithNextInfo(nextShowInfo: SendGiftData) {
//        if (status == STATUS_HIDE || status == STATUS_INACTIVE) {
//            return
//        }
        mNextShowInfo = nextShowInfo
        status = STATUS_ENTER
        coroutineScope.launch(Dispatchers.Main) {
            hide(flow)
            onHide(this@GiftMessageItem, mCurrentShowInfo)
            status = STATUS_INACTIVE
            mNextShowInfo = null
            show(nextShowInfo)
        }
    }

    private fun bindingItem(binding: ItemFlowSendFlowersBinding, info: SendGiftData) {
        binding.itemSendGift.setBackgroundResource(
            if (info.isMe) {
                R.drawable.bg_send_gift_flow_mine
            } else {
                R.drawable.bg_send_gift_flow
            }
        )
        glide.clear(binding.ivUserAvatar)
        glide.load(info.portrait)
            .placeholder(R.drawable.icon_default_avatar)
            .into(binding.ivUserAvatar)
        if (info.sendGiftCount == 1) {
            binding.tvSendFlowerDesc.setTextWithArgs(
                R.string.send_flower_dialog_flow_send_a_flowers,
                (info.nickname ?: "")
            )
        } else {
            binding.tvSendFlowerDesc.setTextWithArgs(
                R.string.send_flower_dialog_flow_send_n_flowers,
                (info.nickname ?: ""),
                info.sendGiftCount
            )
        }
        binding.tvSendFlowerCount.text = "x${info.sendGiftCount}"
    }

    private suspend fun enter(binding: ItemFlowSendFlowersBinding) {
        binding.root.translationX = -binding.root.width.toFloat()
        val transitionAnim: Transition = Slide(Gravity.START)
        transitionAnim.duration = ENTER_DURATION
        transitionAnim.addTarget(binding.root)
        TransitionManager.beginDelayedTransition(
            binding.root.parent as ViewGroup,
            transitionAnim
        )
        binding.root.visibility = View.VISIBLE
        binding.root.translationX = 0f
        // transitionAnim.addListener 的 onTransitionEnd 不一定会回调, 所以用delay
        delay(ENTER_DURATION)
    }

    /**
     * 当前方法使用 ChangeTransform 和 TransitionManager 缩放会在一些鸿蒙 3.0 的手机上出现畸变
     * 所以用 ValueAnimator
     */
    private suspend fun scaleUpCount(binding: ItemFlowSendFlowersBinding) {
        val animator = ValueAnimator.ofFloat(1f, 1.5f)
        animator.addUpdateListener(lifecycleOwner) { animation ->
            val value = animation.getAnimatedValue() as Float
            binding.tvSendFlowerCount.scaleX = value
            binding.tvSendFlowerCount.scaleY = value
            binding.tvSendFlowerCount.background.invalidateSelf()
        }
        animator.duration = COUNT_SCALE_UP_DURATION
        animator.start()

        delay(COUNT_SCALE_UP_DURATION)
    }

    /**
     * 当前方法使用 ChangeTransform 和 TransitionManager 缩放会在一些鸿蒙 3.0 的手机上出现畸变
     * 所以用 ValueAnimator
     */
    private suspend fun scaleDownCount(binding: ItemFlowSendFlowersBinding) {
        val animator = ValueAnimator.ofFloat(1.5f, 1f)
        animator.addUpdateListener(lifecycleOwner) { animation ->
            val value = animation.getAnimatedValue() as Float
            binding.tvSendFlowerCount.scaleX = value
            binding.tvSendFlowerCount.scaleY = value
            binding.tvSendFlowerCount.background.invalidateSelf()
        }
        animator.duration = COUNT_SCALE_DOWN_DURATION
        animator.start()

        delay(COUNT_SCALE_DOWN_DURATION)
    }

    private suspend fun hide(binding: ItemFlowSendFlowersBinding) {
        val transitionAnim: Transition = Fade()
        transitionAnim.duration = HIDE_DURATION
        transitionAnim.addTarget(binding.root)
        TransitionManager.beginDelayedTransition(
            binding.root.parent as ViewGroup,
            transitionAnim
        )
        binding.root.visibility = View.INVISIBLE
        // transitionAnim.addListener 的 onTransitionEnd 不一定会回调, 所以用delay
        delay(HIDE_DURATION)
    }
}
