package com.socialplay.gpark.ui.view.floatnotice

import android.animation.Animator
import android.app.Activity
import android.content.Context
import android.graphics.PixelFormat
import android.view.*
import android.view.MotionEvent.ACTION_DOWN
import android.view.MotionEvent.ACTION_MOVE
import android.view.MotionEvent.ACTION_UP
import android.view.animation.DecelerateInterpolator
import android.widget.FrameLayout
import androidx.core.view.ViewCompat
import androidx.core.view.postDelayed
import androidx.viewbinding.ViewBinding
import com.bumptech.glide.Glide
import com.ly123.tes.mgs.metacloud.model.Message
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.FloatNoticeInteractor
import com.socialplay.gpark.data.interactor.FloatNoticeInteractor.Companion.TYPE_IM_GROUP_MESSAGE
import com.socialplay.gpark.data.interactor.FloatNoticeInteractor.Companion.TYPE_IM_PRIVATE_MESSAGE
import com.socialplay.gpark.data.model.FloatNoticeShowData
import com.socialplay.gpark.databinding.FloatNoticeLayoutBinding
import com.socialplay.gpark.databinding.FloatNoticeMessageLayoutBinding
import com.socialplay.gpark.databinding.FloatNoticeReceiveImMessageLayoutBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.ScreenUtil.dp2px
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.StatusBarUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getString
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.goneIfValueEmpty
import com.socialplay.gpark.util.extension.setPaddingEx
import com.socialplay.gpark.util.extension.visible
import timber.log.Timber

class FloatNoticeView(context: Context, private val type: Int = TYPE_V1) : FrameLayout(context),
    View.OnTouchListener {
    private var location = ""
    var isAutoDisMiss: Boolean? = true
    private var end = false
    private val transY: Float
    var bind: ViewBinding
    var data: FloatNoticeShowData? = null
    val viewTop: Int

    init {
        id = ViewCompat.generateViewId()
        clipToPadding = false
        val extraTop = dp2px(context, 16f)
        viewTop = if (ScreenUtil.isHorizontalScreen(context)) {
            extraTop
        } else {
            StatusBarUtil.getStatusBarHeight(context)
        }
        setPaddingEx(top = viewTop)
        bind = if (type == TYPE_V2) {
            transY = -(dp2px(context, 89.0f) + viewTop).toFloat()
            FloatNoticeMessageLayoutBinding.inflate(LayoutInflater.from(context), this, true)
        } else if (type == TYPE_IM_RECEIVE_MESSAGE) {
            transY = -(dp2px(context, 89.0f) + viewTop).toFloat()
            FloatNoticeReceiveImMessageLayoutBinding.inflate(
                LayoutInflater.from(context),
                this,
                true
            )
        } else {
            transY = -(dp2px(context, 89.0f) + viewTop).toFloat()
            FloatNoticeLayoutBinding.inflate(LayoutInflater.from(context), this, true)
        }
        this.setOnTouchListener(this)
    }

    fun getViewWidth(): Int {
        return if (this.layoutParams.width == -1) {
            ScreenUtil.screenWidth
        } else {
            this.layoutParams.width
        }
    }


    private var action: ((isAgree: Int) -> Unit)? = null

    private var activity: Activity? = null

    companion object {
        const val ACTION_CANCEL = -2
        const val ACTION_SLIDE_CANCEL = -1
        const val ACTION_AGREE = 0
        const val ACTION_REFUSE = 1
        const val TYPE_V1 = 1
        const val TYPE_V2 = 2

        /**
         * 收到IM新消息的通知样式
         */
        const val TYPE_IM_RECEIVE_MESSAGE = 3

        fun showView(
            resourceContext: Context,
            activity: Activity,
            isAutoDisMiss: Boolean = true,
            type: Int = TYPE_V1
        ): FloatNoticeView {
            val floatNoticeView = FloatNoticeView(resourceContext, type)
            floatNoticeView.activity = activity
            floatNoticeView.isAutoDisMiss = isAutoDisMiss
            val rootParams = floatNoticeView.bind.root.layoutParams
            if (ScreenUtil.isHorizontalScreen(resourceContext)) {
                rootParams.width = 351.dp
            } else {
                rootParams.width = WindowManager.LayoutParams.MATCH_PARENT
            }
            floatNoticeView.layoutParams = rootParams
            activity.windowManager.addView(floatNoticeView, getParams(resourceContext))
            return floatNoticeView
        }


        private fun getParams(context: Context): WindowManager.LayoutParams {
            val layoutParams = WindowManager.LayoutParams()
            if (ScreenUtil.isHorizontalScreen(context)) {
                layoutParams.width = WindowManager.LayoutParams.WRAP_CONTENT
            } else {
                layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            }
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.flags = (WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                    or WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                    or WindowManager.LayoutParams.FLAG_LAYOUT_INSET_DECOR
                    or WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
                    or WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH
                    or WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS // 防止状态栏遮挡悬浮球
                    or WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION) // 防止导航栏遮挡悬浮球

            layoutParams.type = WindowManager.LayoutParams.LAST_APPLICATION_WINDOW
            layoutParams.gravity = Gravity.CENTER or Gravity.TOP
            layoutParams.format = PixelFormat.RGBA_8888
            return layoutParams
        }

    }


    fun hideView() {
        kotlin.runCatching { activity?.windowManager?.removeView(this) }
    }

    /**
     * 因为弹窗的宽度是一样的，所以需要根据横竖屏进行计算
     * 如果屏幕宽>屏幕高度 是横屏 以当前高度计算 弹框宽度，否则反之
     *
     * @param context
     * @return
     */
    private fun getPadding(context: Context): Int {
        val displayMetrics = context.resources.displayMetrics
        val screenWidth = displayMetrics.widthPixels
        val screenHeight = displayMetrics.heightPixels
        return if (screenWidth > screenHeight) {
            ((screenWidth - screenHeight * 0.93) / 2).toInt()
        } else {
            ((screenWidth - screenWidth * 0.93) / 2).toInt()
        }
    }

    /**
     * 设置要展示的数据
     *
     * @param data
     * @param action
     * @receiver
     */
    fun setData(
        data: FloatNoticeShowData,
        isInGame: Boolean,
        onShowFloat: (() -> Unit)? = null,
        action: (isAgree: Int) -> Unit
    ) {
        this.action = action
        this.data = data
        this.data?.type = type
        when (type) {
            TYPE_IM_RECEIVE_MESSAGE -> {
                (bind as? FloatNoticeReceiveImMessageLayoutBinding)?.apply {
                    val placeholder = if(data.imType == FloatNoticeInteractor.TYPE_IM_PRIVATE_MESSAGE){
                        R.drawable.icon_default_avatar
                    }else if(data.imType == FloatNoticeInteractor.TYPE_IM_GROUP_MESSAGE){
                        R.drawable.icon_item_group_chat_avatar
                    }else{
                        R.drawable.placeholder_corner_360
                    }
                    Glide.with(context)
                        .load(data.icon)
                        .circleCrop()
                        .placeholder(placeholder)
                        .into(ivAvatar)

                    tvTitle.text = data.title

                    val atText = when (data.atType) {
                        Message.ATType.AT_ME, Message.ATType.AT_ALL_AND_AT_ME -> getString(R.string.conversation_content_prefix_at_you)
                        Message.ATType.AT_ALL -> getString(R.string.conversation_content_prefix_at_all)
                        else -> ""
                    }
                    if (atText.isEmpty()) {
                        tvContent.text = data.contentText
                    } else {
                        tvContent.text = SpannableHelper.Builder()
                            .text(atText)
                            .colorRes(R.color.color_FF5F42)
                            .text(data.contentText)
                            .textAppearance(context, R.style.MetaTextView_S12_PoppinsRegular400)
                            .colorRes(R.color.color_666666)
                            .build()
                    }
                    if (PandoraToggle.isIMTipsClose) {
                        Analytics.track(EventConstants.EVENT_IM_PRIVATE_CLOSE_SHOW)
                        layoutCloseNotification.visible()
                        layoutCloseNotification.setOnClickListener {
                            hideView()
                            action.invoke(ACTION_REFUSE)
                        }
                    } else {
                        layoutCloseNotification.gone()
                    }
                    layoutMessageReplay.setOnClickListener {
                        if (!data.isClickDismiss) {
                            //点击暂停消失
                            end = true
                            <EMAIL> = false
                        } else {
                            hideView()
                        }
                        action.invoke(ACTION_AGREE)
                    }
                    floatNoticeRootLayout.setOnTouchListener(this@FloatNoticeView)
                }
            }
            TYPE_V2 -> {
                (bind as? FloatNoticeMessageLayoutBinding)?.apply {
                    Glide.with(context).load(data.icon).circleCrop()
                        .placeholder(R.drawable.placeholder_corner_360).into(ivUserHead)
                    tvInviteUserName.text = data.title
                    if (data.inviteText.isEmpty()) {
                        tvInviteTitle.gone()
                    } else {
                        tvInviteTitle.visible()

                        val atText = when (data.atType) {
                            Message.ATType.AT_ME, Message.ATType.AT_ALL_AND_AT_ME -> getString(R.string.conversation_content_prefix_at_you)
                            Message.ATType.AT_ALL -> getString(R.string.conversation_content_prefix_at_all)
                            else -> ""
                        }
                        if (atText.isEmpty()) {
                            tvInviteTitle.text = data.inviteText
                        } else {
                            tvInviteTitle.text = SpannableHelper.Builder()
                                .text(atText)
                                .textAppearance(
                                    context,
                                    R.style.MetaTextView_S12_PoppinsSemiBold600
                                )
                                .colorRes(R.color.color_FF5F42)
                                .text(data.inviteText)
                                .textAppearance(context, R.style.MetaTextView_S12_PoppinsRegular400)
                                .colorRes(R.color.color_666666)
                                .build()
                        }
                    }
                    btnInviteAgree.text = data.agreeText

                    val imType = <EMAIL>?.imType
                    if ((imType == TYPE_IM_PRIVATE_MESSAGE || imType == TYPE_IM_GROUP_MESSAGE) && PandoraToggle.isIMTipsClose) {
                        btnInviteRefuse.visible(false)
                        imgImTips.visible(true)
                        Analytics.track(EventConstants.EVENT_IM_PRIVATE_CLOSE_SHOW)
                    } else {
                        btnInviteRefuse.goneIfValueEmpty(data.refuseText)
                        imgImTips.gone()
                    }
                    btnInviteAgree.setOnClickListener {
                        if (!data.isClickDismiss) {
                            //点击暂停消失
                            end = true
                            <EMAIL> = false
                        } else {
                            hideView()
                        }
                        action.invoke(ACTION_AGREE)
                    }
                    btnInviteRefuse.setOnClickListener {
                        hideView()
                        action.invoke(ACTION_REFUSE)
                    }
                    imgImTips.setOnClickListener{
                        hideView()
                        action.invoke(ACTION_REFUSE)
                    }
                    floatNoticeRootLayout.setOnTouchListener(this@FloatNoticeView)
                }
            }

            else    -> {
                (bind as? FloatNoticeLayoutBinding)?.apply {
                    Glide.with(context).load(data.icon).circleCrop()
                        .placeholder(R.drawable.placeholder_corner_360).into(ivUserHead)
                    tvInviteUserName.text = data.title
                    tvInviteTitle.goneIfValueEmpty(data.inviteText)
                    tvInviteContent.goneIfValueEmpty(data.gameName)
                    btnInviteAgree.text = data.agreeText
                    btnInviteAgree.setOnClickListener {
                        hideView()
                        action.invoke(ACTION_AGREE)
                    }
                    btnInviteRefuse.setOnClickListener {
                        hideView()
                        action.invoke(ACTION_REFUSE)
                    }
                    floatNoticeRootLayout.setOnTouchListener(this@FloatNoticeView)
                }
            }
        }
        onShowFloat?.invoke()
        translationY = transY
        animIn()
        if (isAutoDisMiss == true) {
            postDelayed(5_000L) {
                if (!end) {
                    end = true
                    isEnable(false)
                    animOut()
                }
            }
        }

    }
    private fun isEnable(enable:Boolean){
        when (type) {
            TYPE_V2 -> {
                (bind as? FloatNoticeMessageLayoutBinding)?.apply {
                    btnInviteAgree.isEnabled = enable
                }
            }
        }
    }

    /**
     * get the predefine animation used to show float window
     */
    private fun animIn(duration: Long = 300L) {
        animate()
            .translationY(0.0f)
            .setDuration(duration)
    }

    private fun animOut(): ViewPropertyAnimator {
        return animate()
            .translationY(transY)
            .setDuration(200L)
            .setInterpolator(DecelerateInterpolator())
            .setListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator) {

                }

                override fun onAnimationEnd(animation: Animator) {
                    gone()
                    hideView()
                    isEnable(true)
                    action?.invoke(ACTION_CANCEL)
                }

                override fun onAnimationCancel(animation: Animator) {}

                override fun onAnimationRepeat(animation: Animator) {}
            })
    }

    private var y1: Float = 0f

    override fun onTouch(v: View, event: MotionEvent): Boolean {
        Timber.d("onFling_ onTouch")
        when (event.action) {
            ACTION_DOWN -> {
                y1 = event.y
                Timber.d("onFling_ ACTION_DOWN")
            }

            ACTION_MOVE -> {
                if (!end) {
                    v.translationY = (event.y - y1).coerceAtMost(0F)
                }
            }

            ACTION_CANCEL -> {
                if (!end) {
                    animIn(50L)
                }
            }

            ACTION_UP -> {
                if (!end) {
                    if (y1 - event.y > 50.0f) {
                        end = true
                        animOut()
                        return true
                    } else {
                        animIn(50L)
                    }
                }
            }
        }
        return false
    }


    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        activity = null
    }

}