package com.socialplay.gpark.ui.developer

import android.app.Dialog
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import com.bin.cpbus.CpEventBus
import com.bumptech.glide.Glide
import com.google.android.exoplayer2.MediaItem
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.LifecycleInteractor
import com.socialplay.gpark.data.interactor.UploadFileInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.LoginSource
import com.socialplay.gpark.data.model.SimpleListData
import com.socialplay.gpark.data.model.post.PostShareDetail
import com.socialplay.gpark.data.model.share.ShareContent
import com.socialplay.gpark.databinding.FragmentDemoBinding
import com.socialplay.gpark.databinding.ViewPlotRecordLoadingBinding
import com.socialplay.gpark.function.exoplayer.SharedVideoPlayerControllerInteractor
import com.socialplay.gpark.function.overseabridge.bridge.IUpdateBride
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.function.share.platform.SystemShare
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.dialog.ListDialog
import com.socialplay.gpark.ui.permission.Permission
import com.socialplay.gpark.ui.permission.PermissionRequest
import com.socialplay.gpark.util.ClipBoardUtil
import com.socialplay.gpark.util.CustomMediaPlayerEngine
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.PictureSelectorUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.glide.GlideEngine
import com.socialplay.gpark.util.glide.LubanCompressEngine
import com.socialplay.gpark.util.property.viewBinding
import com.socialplay.gpark.util.toJSON
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.koin.android.ext.android.inject
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.io.File


/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * e-mail : <EMAIL>
 * time   : 2021/05/11
 * desc   :
 * </pre>
 */


class DemoFragment : BaseFragment<FragmentDemoBinding>() {

    private val accountInteractor: AccountInteractor by inject()
    private val uploadFileInteractor: UploadFileInteractor by inject()
    private val metaKV: MetaKV by inject()
    private val mmkv: MMKV = MMKV.mmkvWithID("CpEventBus", MMKV.MULTI_PROCESS_MODE)!!
    private var i = 1L

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentDemoBinding? {
        return FragmentDemoBinding.inflate(inflater, container, false)
    }

    override fun init() {
        binding.btnGoChoice.setOnAntiViolenceClickListener {
            MetaRouter.Recommend.goChoiceHome(this)
        }
        binding.btnTestCanUpdate.setOnClickListener {
            viewLifecycleOwner.lifecycleScope.launch {
                val canUpdate = GlobalContext.get().get<IUpdateBride>().updateAvailability(requireContext())
                binding.btnTestCanUpdate.text = "canUpdate?: $canUpdate"
            }
        }
        binding.testClipGet.setOnAntiViolenceClickListener {
            viewLifecycleOwner.lifecycleScope.launch {
                toast(ClipBoardUtil.getClipBoardContent(requireContext()))
            }
        }
        binding.testClipClear.setOnAntiViolenceClickListener {
            viewLifecycleOwner.lifecycleScope.launch {
                ClipBoardUtil.clearClipBoard(requireContext())
                toast(ClipBoardUtil.getClipBoardContent(requireContext()))
            }
        }
        binding.testClipSet.setOnAntiViolenceClickListener {
            viewLifecycleOwner.lifecycleScope.launch {
                ClipBoardUtil.setClipBoardContent("test clip", requireContext())
                toast(ClipBoardUtil.getClipBoardContent(requireContext()))
            }
        }
        binding.btnTestLocale.setOnAntiViolenceClickListener {
            Toast.makeText(context?.applicationContext, R.string.copy_success, Toast.LENGTH_SHORT).show()
//            val dm: DisplayMetrics = resources.displayMetrics
//            val config = resources.configuration.apply {
//                setLocale(Locale.ENGLISH)
//            }
//            resources.updateConfiguration(config, dm) // 不加这句切换不成功
//            context?.createConfigurationContext(config)
        }
        binding.btnTestDialogWeb.setOnAntiViolenceClickListener {
            val webUrl = binding.etDialogWeb.text.toString()
            val curActivity =
                LifecycleInteractor.activityRef?.get() ?: return@setOnAntiViolenceClickListener
            (curActivity as? FragmentActivity)?.apply {
                MetaRouter.Web.showDialog(this, webUrl, null)
            }
        }
        binding.btnTestCp.setOnClickListener {
            viewLifecycleOwner.lifecycleScope.launch {
                val list = ArrayList<String>()
                repeat(10000) {
                    list.add("$i asuhfiasuhfiusahifuhasuifhuiashfuiashifuhashuifuiashfuiashfuiahuihasiuhfuiashfiuhsauifhiasuhfuiashifuhasuifhuiashfuiashuifhasuifhasuifhasuhfiasuhfiusahifuhasuifhuiashfuiashifuhashuifuiashfuiashfuiahuihasiuhfuiashfiuhsauifhiasuhfuiashifuhasuifhuiashfuiashuifhasuifhasuifhasuhfiasuhfiusahifuhasuifhuiashfuiashifuhashuifuiashfuiashfuiahuihasiuhfuiashfiuhsauifhiasuhfuiashifuhasuifhuiashfuiashuifhasuifhasuifhasuhfiasuhfiusahifuhasuifhuiashfuiashifuhashuifuiashfuiashfuiahuihasiuhfuiashfiuhsauifhiasuhfuiashifuhasuifhuiashfuiashuifhasuifhasuifhasuhfiasuhfiusahifuhasuifhuiashfuiashifuhashuifuiashfuiashfuiahuihasiuhfuiashfiuhsauifhiasuhfuiashifuhasuifhuiashfuiashuifhasuifhasuifhasuhfiasuhfiusahifuhasuifhuiashfuiashifuhashuifuiashfuiashfuiahuihasiuhfuiashfiuhsauifhiasuhfuiashifuhasuifhuiashfuiashuifhasuifhasuifh")
                }
                val event = list.toJSON()!!
                Timber.i("event size ${event.length}")
                ++i
                CpEventBus.post(event)
                binding.btnTestCp.text = "cpEvent bus total: ${mmkv.totalSize() / 1024 / 1024}Mb actualSize: ${mmkv.actualSize() / 1024 / 1024}"
            }
        }
        binding.btnTestCp.text = "cpEvent bus total: ${mmkv.totalSize() / 1024 / 1024}Mb actualSize: ${mmkv.actualSize() / 1024 / 1024}"
        binding.btnTestMMKV.setOnClickListener {
            mmkv.clearAll()
            binding.btnTestCp.text = "cpEvent bus total: ${mmkv.totalSize() / 1024 / 1024}Mb actualSize: ${mmkv.actualSize() / 1024 / 1024}"
        }
        binding.btnGoShare.setOnClickListener {
            MetaRouter.Share.share(
                this,
                ShareContent(
                    ShareContent.TYPE_POST,
                    GsonUtil.safeToJson(
                        PostShareDetail(
                            "ssdr",
                            "hellllocoeocmefv",
                            "https://qn-check-platform-user.metaworld.fun/test/d50ab7af09cb444aa9db1aa49acfd59c_8480.jpeg",
                            null
                        )
                    )
                ),
                "demo"
            ) { success, uuid ->
                Timber.d("share ${if (success) "success" else "failed"}, to: $uuid")

            }
        }
        binding.btnGoSquare.setOnClickListener {
            MetaRouter.Post.topicSquare(this)
        }

        binding.btnUploadTest.setOnAntiViolenceClickListener {
            PictureSelector.create(this)
                .openGallery(SelectMimeType.TYPE_ALL)
                .setMaxSelectNum(9)
                .setMaxVideoSelectNum(1)
                .setImageEngine(GlideEngine)
                .setCompressEngine(LubanCompressEngine())
                .setVideoPlayerEngine(CustomMediaPlayerEngine())
                .setSelectorUIStyle(PictureSelectorUtil.getCommonStyle(requireContext()))
                .forResult(object : OnResultCallbackListener<LocalMedia> {
                    override fun onResult(result: ArrayList<LocalMedia>?) {
                        result ?: return
                        Timber.d("PictureSelector.onResult ${result.size}")
                        val compressedSet = HashSet<String>()
                        val files = buildList {
                            result.forEachIndexed { index, localMedia ->
                                val path = localMedia.compressPath ?: localMedia.realPath ?: localMedia.path ?: return@forEachIndexed
                                Timber.d("PictureSelector.onResult $index $path")
                                if (localMedia.isCompressed && !localMedia.compressPath.isNullOrBlank()) {
                                    compressedSet.add(path)
                                }
                                add(File(path))
                            }
                        }
                        if (files.isNotEmpty()) {
                            viewLifecycleOwner.lifecycleScope.launch {
                                uploadFileInteractor.uploadList1By1(
                                    UploadFileInteractor.BIZ_CODE_COMMUNITY,
                                    files,
                                    taskId = "omfg",
                                    progressHandler = { taskId, curCount, totalCount, path, percent ->
                                        Timber.d("PictureSelector.uploadList1By1 $taskId, $curCount, $totalCount, $path, $percent")
                                    },
                                    compressHandler = {
                                        if (compressedSet.contains(it.absolutePath)) {
                                            it
                                        } else {
                                            uploadFileInteractor.compress(it)
                                        }
                                    }
                                ).collect {
                                    Timber.d("PictureSelector.uploadResult ${it.data}")
                                }
                            }
                        }
                    }

                    override fun onCancel() {}
                })
        }
        binding.btnClearSuper.setOnAntiViolenceClickListener {
            metaKV.appKV.isSuggestGameAlreadyShow = false
            toast("clear success!")
        }
        binding.ivUnLike.setOnAntiViolenceClickListener {
            startActivity(Intent(Intent.ACTION_VIEW, Uri.parse("https://m.baidu.com")))
        }
        binding.btnLogin.setOnAntiViolenceClickListener {
//            val intent = Intent(context, StyleTestActivity::class.java)
//            startActivity(intent)
        }
        binding.btnSignup.setOnAntiViolenceClickListener {
            MetaRouter.Account.signUp(this, LoginSource.Demo.source)
        }
        binding.btnGameDetail.setOnAntiViolenceClickListener {
//            MetaRouter.Detail.game(
//                this,
//                113061,
//                ResIdBean(),
//                "",
//                "https://game-detail-online.233xyx.com/233game/data/v1/113061/**********.json"
//            )
        }
        binding.btnGoAiBot.setOnAntiViolenceClickListener {

        }
        binding.btnSearch.setOnAntiViolenceClickListener {
            MetaRouter.Search.navigate(this)
        }
        binding.btnWeb.setOnAntiViolenceClickListener {
            //todo 测试
            //originWebUrl = "https://m.233leyuan.com/webserver/gameserv/GameDetail/feedback.html?gameName=樱花校园模拟器2021&gameId=77793&isGlobal=false&uuid=69e7f9e06f3542ce95b89414fd8f41c0&onlyId=19b24364d6e52f22&appVersionCode=2640000&channelId=default&systemVersion=9&deviceBrand=samsung&"
            //originWebUrl = "https://na.233she.cn/#/sakuraBox"
            if (binding.etWeb.text.isNullOrEmpty()) {
                ToastUtil.showShort(requireContext(), "please enter a web address")
                return@setOnAntiViolenceClickListener
            }
            MetaRouter.Web.navigate(this, "test", binding.etWeb.text.toString())
        }
        binding.btnSimpleDialog.setOnAntiViolenceClickListener {
            MetaRouter.Dialog.alert(this)
                .content("sample")
                .cancelBtnTxt("confirm")
                .confirmBtnTxt("fxxk")
                .cancelCallback { ToastUtil.showShort(requireContext(), "left bnt") }
                .confirmCallback { ToastUtil.showShort(requireContext(), "right bnt") }
                .dismissCallback { ToastUtil.showShort(requireContext(), "be gone!! byAction:$it") }
                .navigate()
        }

        binding.btnSimpleListDialog.setOnClickListener {
            ListDialog()
                .list(
                    mutableListOf(
                        SimpleListData("play", R.drawable.bg_common_dialog_yellow),
                        SimpleListData("copy"),
                        SimpleListData("cancel")
                    )
                )
                .title("title")
                .content("content")
                .clickCallback {
                    toast(it?.text.toString())
                }
                .onViewCreateCallback {
                    it.iv.visible()
                    Glide.with(this@DemoFragment).load("").into(it.iv)
                }
                .show(childFragmentManager, "")
        }

        binding.btnPlayer.setOnAntiViolenceClickListener {
//            MetaRouter.Player.navigate(this, url)
        }
        binding.btnBuildConfig.setOnAntiViolenceClickListener {
            MetaRouter.Developer.buildConfig(this)
        }
        binding.btnPermission.setOnAntiViolenceClickListener {
            //            findNavController().navigate(R.id.permissionGoSetting, PermissionGoSettingDialogFragmentArgs().toBundle())
            //            PermissionGoSettingDialogFragment.showSetting(this, "sssssss"){}
            //            PermissionRationaleDialogFragment.showRationale(this,"aaaa"){}
            PermissionRequest.with(requireActivity())
                .permissions(Permission.EXTERNAL_STORAGE, Permission.PHONE_STATE, Permission.COARSE_LOCATION)
                .denied {
                    Timber.d("anxin_aaa  denied")
                }
                .granted {
                    Timber.d("anxin_aaa  granted")
                }
                .enableGoSettingDialog()
                .enableRationaleDialog()
                .request()
        }
        binding.btnMyGameDetail.setOnAntiViolenceClickListener {
        }
        binding.btnTestFireStore.setOnClickListener {
            toast("not impl")
        }
        binding.btnTestFireStoreGet.setOnClickListener {
            toast("not impl")
        }
        binding.btnTestFacebookEvent.setOnClickListener {
            DemoWrapper.clickTestFacebook(requireContext())
        }
        binding.btnTestCrash.setOnAntiViolenceClickListener {
            DemoWrapper.testJavaCrash()
        }
        binding.btnTestCrashNative.visible(EnvConfig.isParty())
        binding.btnTestCrashNative.setOnAntiViolenceClickListener {
            DemoWrapper.testNativeCrash()
        }
        binding.btnTestANR.setOnAntiViolenceClickListener {
            DemoWrapper.testANRCrash()
        }
        binding.btnGoChatSetting.setOnAntiViolenceClickListener {
            MetaRouter.IM.goChatSetting(this, "test_fa47a16c1a6c4d37a57d617356e832d5") { isClearMsg, isDeleteFriend, remark ->
//                Timber.d("用户设置 isClearMsg %s isDeleteFriend %s remark %s ",isClearMsg,isDeleteFriend,remark)
//                if (isDeleteFriend) { // 执行了删除好友操作
//                    findNavController().popBackStack()
//                    return@goChatSetting
//                }
//                if (isClearMsg) { // 执行了清空消息操作
//                    conversationViewModel.cleanAllMessage()
//                }
//                if(TextUtils.isEmpty(remark) || remark == title){
//                    return@goChatSetting
//                }
//                conversationViewModel.updateTitle(remark)
            }
        }
        binding.btnGoBy.setOnAntiViolenceClickListener {
            MetaRouter.Control.navigate(this, R.id.pay)
        }
        binding.btnVideo.setOnAntiViolenceClickListener {
            val playerController by inject<SharedVideoPlayerControllerInteractor>()
            val toRestoreUri = MediaItem.fromUri("https://gameiu.233leyuan.com/game/video/video/v0/12600802/f15f5d9adde8676545e3b490265abea5.mp4")
            playerController.setMediaItem(toRestoreUri)
            playerController.changeMuteState(false)
            MetaRouter.Video.fullScreenPlayer(this, analyticFrom = "demo")
        }
        binding.btnReplace.setOnAntiViolenceClickListener {
            val st = ""
            listOf<String>().forEachIndexed { index, s ->
                st
            }
        }

        binding.btnSysShareVideo.setOnClickListener {
            val file = File(requireContext().externalCacheDir, "video.mp4")
            if (file.exists()) {
                SystemShare.shareVideoFileBySystem(requireContext(), file)
            } else toast("File not exists \n$file")
        }

        binding.btnProgressLoading.setOnClickListener {
            val root = ViewPlotRecordLoadingBinding.inflate(layoutInflater)
            Dialog(requireContext()).apply {
                setContentView(root.root)
            }.show()
        }

        binding.btnScheme.setOnClickListener {
            MetaRouter.Main.dispatchUrl(requireContext(), Uri.parse(binding.etScheme.text.toString()), "demo")
        }

        binding.btnPostDetail.setOnClickListener {
            MetaRouter.Post.goPostDetail(this, binding.etPostId.text.toString(), "demo")
        }

        // Flutter测试按钮
        binding.btnFlutterTest.setOnClickListener {
            val intent = com.socialplay.gpark.flutter.ui.FlutterTestActivity.createIntent(requireContext(), "/test")
            startActivity(intent)
        }

        // Flutter设置按钮
        binding.btnFlutterSettings.setOnClickListener {
            val intent = com.socialplay.gpark.flutter.ui.FlutterSettingsActivity.createIntent(requireContext(), "/settings")
            startActivity(intent)
        }
    }

    @Subscribe
    fun onEvent(list: String) {
        Timber.i("event \n $list")
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        CpEventBus.register(this)
    }

    override fun onDestroyView() {
        CpEventBus.unregister(this)
        super.onDestroyView()
    }

    override fun loadFirstData() {
    }

    override fun getFragmentName(): String {
        return "DemoFragment"
    }
}