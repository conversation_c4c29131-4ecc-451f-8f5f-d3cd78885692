package com.socialplay.gpark.ui.gamedetail.sendflower

import android.os.Bundle
import android.os.SystemClock
import android.text.TextWatcher
import android.view.View
import android.view.ViewTreeObserver
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.Fragment
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.DialogCustomizeFlowerCountBinding
import com.socialplay.gpark.ui.core.BaseDialogFragment
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.IntRangeCallbackFilter
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.ime.KeyboardListener
import com.socialplay.gpark.util.ime.WindowInsetsHelper
import com.socialplay.gpark.util.property.viewBinding

class CustomizeFlowerCountDialog : BaseDialogFragment() {
    override val binding: DialogCustomizeFlowerCountBinding by viewBinding(
        DialogCustomizeFlowerCountBinding::inflate
    )
    private var resultCallback: ((Int) -> Unit)? = null

    companion object {
        fun show(
            fragment: Fragment,
            resultCallback: ((Int) -> Unit)? = null
        ) {
            CustomizeFlowerCountDialog().apply {
                this.resultCallback = resultCallback
            }.show(
                fragment.childFragmentManager,
                "CustomizeFlowerCountDialog"
            )
        }
    }

    override fun getStyle() = R.style.DialogStyleFloating_Input_NoAnimation
    override fun maxWidth(): Int {
        val ctx = context
        ctx ?: return 0
        return (ScreenUtil.getScreenWidth(ctx) * 2 / 3).coerceAtLeast(dp(343))
            .coerceAtMost(ScreenUtil.getScreenWidth(ctx) - dp(30))
    }

    private var windowInsetsHelper: WindowInsetsHelper = WindowInsetsHelper()

    private val keyboardListener = object : KeyboardListener() {
        override fun onProgress(keyboardHeight: Int) {
            if (isBindingAvailable()) {
                binding.dialogLayout.translationY = (-keyboardHeight).toFloat()
            }
        }

        override fun onKeyboardShowEnd() {
            if (isBindingAvailable()) {
                // 在有的手机上偶现键盘高度回一直是0, 这种情况下用缓存的键盘高度做兜底
                if (currentKeyboardHeight > 0) {
                    binding.dialogLayout.translationY = (-currentKeyboardHeight).toFloat()
                } else {
                    val height = keyboardFullHeight ?: 0
                    if (height > 0 && height != currentKeyboardHeight) {
                        binding.dialogLayout.translationY = (-height).toFloat()
                    }
                }
            }
        }

        override fun onKeyboardHideEnd() {
            if (isBindingAvailable()) {
                binding.dialogLayout.translationY = 0f
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val window = dialog?.window
        if (window != null) {
            windowInsetsHelper.apply(
                window,
                binding.root,
                keyboardListener
            )
        }
    }

    private var showKeyboardFirstTime = true
    private var textWatcher: TextWatcher? = null
    override fun onResume() {
        super.onResume()
        if (showKeyboardFirstTime) {
            showKeyboardFirstTime = false
            binding.etInput.requestFocusFromTouch()
            // DialogFragment 在 onResume 的时候可能还没准备好接收输入法, 所以需要等到 WindowFocusChange 的时候做一次兜底
            dialog?.window?.decorView?.viewTreeObserver?.addOnWindowFocusChangeListener(
                object : ViewTreeObserver.OnWindowFocusChangeListener {
                    override fun onWindowFocusChanged(hasFocus: Boolean) {
                        if (hasFocus) {
                            InputUtil.showSoftBoard(binding.etInput)
                            dialog?.window?.decorView?.viewTreeObserver?.removeOnWindowFocusChangeListener(
                                this
                            )
                        }
                    }
                })
            InputUtil.showSoftBoard(binding.etInput)
        }
    }

    private var toastTs = 0L
    override fun init() {
        binding.etInput.filters = arrayOf(IntRangeCallbackFilter(min = 1, max = 10000) {
            context?.let {
                val curTs = SystemClock.elapsedRealtime()
                if (curTs - toastTs > 2000) {
                    toastTs = curTs
                    toast(R.string.dialog_customize_flower_count_input_desc)
                }
            }
        })

        binding.root.setOnClickListener {
            dismissAllowingStateLoss()
        }
        binding.dialogLayout.isClickable = true


        binding.tvConfirm.isEnabled = false
        binding.tvConfirm.setBackgroundResource(R.drawable.bg_f0f0f0_corner_40)
        binding.tvConfirm.setTextColorByRes(R.color.color_999999)

        textWatcher = binding.etInput.doAfterTextChanged { text ->
            val hasContent = !text.isNullOrBlank()
            setTvConfirmEnable(hasContent)
        }
        // 自定义数量默认为1
        binding.etInput.setText("1")

        binding.ivClearText.setOnClickListener {
            binding.etInput.text?.clear()
        }
        binding.tvConfirm.setOnAntiViolenceClickListener {
            val input = binding.etInput.text?.toString() ?: return@setOnAntiViolenceClickListener
            try {
                val count = input.toInt()
                dismissWithResult(count)
            } catch (_: NumberFormatException) {
                toast(R.string.dialog_customize_flower_count_input_desc)
            }
        }
        binding.tvCancel.setOnAntiViolenceClickListener {
            dismissAllowingStateLoss()
        }
    }

    private fun setTvConfirmEnable(enable: Boolean) {
        if (enable) {
            binding.tvConfirm.isEnabled = true
            binding.tvConfirm.setBackgroundResource(R.drawable.bg_ffef30_round_40)
            binding.tvConfirm.setTextColorByRes(R.color.color_1A1A1A)
        } else {
            binding.tvConfirm.isEnabled = false
            binding.tvConfirm.setBackgroundResource(R.drawable.bg_f0f0f0_corner_40)
            binding.tvConfirm.setTextColorByRes(R.color.color_999999)
        }
    }

    private fun dismissWithResult(count: Int) {
        resultCallback?.invoke(count)
        resultCallback = null
        dismissAllowingStateLoss()
    }

    override fun onDestroyView() {
        if (textWatcher != null) {
            binding.etInput.removeTextChangedListener(textWatcher)
            textWatcher = null
        }
        windowInsetsHelper.unApply()
        super.onDestroyView()
    }
}