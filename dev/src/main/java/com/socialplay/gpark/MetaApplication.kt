package com.socialplay.gpark

import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.Bundle
import androidx.paging.ExperimentalPagingApi
import com.socialplay.gpark.app.ApplicationLifeCycle
import com.socialplay.gpark.app.MetaApplicationLifecycle
import com.socialplay.gpark.function.analytics.handle.AppLaunchAnalytics
import com.socialplay.gpark.function.locale.MetaLanguages
import com.socialplay.gpark.ui.main.HomeImageShowAnalytics
import com.socialplay.gpark.flutter.FlutterBridgeManager
import timber.log.Timber

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/09
 * desc   :
 * </pre>
 */


@ExperimentalPagingApi
class MetaApplication : Application() {

    private lateinit var applicationLifecycle: ApplicationLifeCycle

    override fun attachBaseContext(base: Context?) {
        super.attachBaseContext(MetaLanguages.attachContext(base))
        HomeImageShowAnalytics.startBootTime = System.currentTimeMillis()
        AppLaunchAnalytics.handleAppOnAttachBefore()
        applicationLifecycle = MetaApplicationLifecycle(this)
        applicationLifecycle.attachContext()

        // 初始化Flutter引擎
        try {
            FlutterBridgeManager.getInstance().initialize(this)
            Timber.d("Flutter engine initialized in Application")
        } catch (e: Exception) {
            Timber.e(e, "Failed to initialize Flutter engine in Application")
        }

        AppLaunchAnalytics.handleAppOnAttachAfter()
    }

    override fun onCreate() {
        AppLaunchAnalytics.handleAppOnCreateBefore()
        super.onCreate()
        applicationLifecycle.onCreate()
        AppLaunchAnalytics.handleAppOnCreateAfter()
    }

    override fun onTerminate() {
        super.onTerminate()
        applicationLifecycle.onTerminate()
    }
}