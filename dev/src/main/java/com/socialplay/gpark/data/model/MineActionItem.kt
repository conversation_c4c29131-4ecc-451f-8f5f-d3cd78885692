package com.socialplay.gpark.data.model

import android.os.Bundle
import androidx.annotation.DrawableRes
import androidx.annotation.IdRes
import androidx.annotation.StringRes
import com.meta.pandora.data.entity.Event
import com.meta.box.biz.h5config.model.H5PageConfigItem

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/14
 * desc   :
 * </pre>
 */


data class MineActionItem(
    @StringRes val displayNameResId: Int,
    val jump: MineActionJump,
    var showLabelRedDot: Boolean = false
)

sealed class MineActionJump {
    data class Url(val url: String, val event: Event, val source: String, var isShow: Boolean = true, var showStatusBar: Boolean = true) : MineActionJump()
    data class UrlItem(val item: H5PageConfigItem, val event: Event, val source: String, var isShow: Boolean = true) : MineActionJump()
    data class GraphNav(@IdRes val graphDestId: Int, val event: Event? = null, val navExtra: Bundle? = null) : MineActionJump()
    data class AccountSettingActionItem(val event: Event) : MineActionJump()
    data class LogoutActionItem(val event: Event) : MineActionJump()
    data class CompleteAccountItem(val event: Event, val source: String) : MineActionJump()
    data class RealName(val event: Event) : MineActionJump()
    data class Update(val event: Event) : MineActionJump()
    data class Space(val heightDp: Int) : MineActionJump()
}
