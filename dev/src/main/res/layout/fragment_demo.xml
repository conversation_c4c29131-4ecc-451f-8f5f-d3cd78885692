<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ScrollView
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:orientation="vertical"
            android:divider="@drawable/post_reply_shape_divider"
            android:showDividers="middle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <Button
                android:id="@+id/btnGoChoice"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="btn Go Choice"
                app:layout_constraintTop_toTopOf="parent" />

            <Button
                android:id="@+id/btnTestCanUpdate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="test can update"
                app:layout_constraintTop_toTopOf="parent" />

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/ivUnLike"
                android:layout_width="48dp"
                app:lottie_autoPlay="false"
                app:lottie_loop="true"
                android:background="#f1f1f1"
                android:layout_height="48dp"/>
            <Button
                android:id="@+id/btnTestLocale"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_set_locale" />

            <EditText
                android:id="@+id/etDialogWeb"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/debug_web_hint"/>
            <Button
                android:id="@+id/testClipGet"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_test_clip_get"
                app:layout_constraintTop_toTopOf="parent" />
            <Button
                android:id="@+id/testClipClear"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_test_clip_clear"
                app:layout_constraintTop_toTopOf="parent" />
            <Button
                android:id="@+id/testClipSet"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_test_clip_set"
                app:layout_constraintTop_toTopOf="parent" />
            <Button
                android:id="@+id/btnTestDialogWeb"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_test_web_dialog"
                app:layout_constraintTop_toTopOf="parent" />
            <Button
                android:id="@+id/btnTestCp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_test_cpeventbus_crash"
                app:layout_constraintTop_toTopOf="parent" />
            <Button
                android:id="@+id/btnTestMMKV"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_test_clear_mmkv"
                app:layout_constraintTop_toTopOf="parent" />

            <Button
                android:id="@+id/btnUploadTest"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_test_upload" />

            <Button
                android:id="@+id/btnClearSuper"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_clear_super"
                app:layout_constraintTop_toTopOf="parent" />

            <Button
                android:id="@+id/btnLogin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_login"
                app:layout_constraintTop_toTopOf="parent" />

            <Button
                android:id="@+id/btnSearch"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_search"
                app:layout_constraintTop_toBottomOf="@+id/btnLogin" />

            <Button
                android:id="@+id/btnGameDetail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_open_detail"
                app:layout_constraintTop_toBottomOf="@id/btnSearch" />
            <EditText
                android:id="@+id/et_web"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/debug_web_hint"/>
            <Button
                android:id="@+id/btnWeb"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_open_web"
                app:layout_constraintTop_toBottomOf="@id/btnGameDetail" />

            <EditText
                android:id="@+id/etScheme"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/debug_uri_hint"/>
            <Button
                android:id="@+id/btnScheme"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_scheme" />

            <EditText
                android:id="@+id/etPostId"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/debug_post_id_hint"/>
            <Button
                android:id="@+id/btnPostDetail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_post_detail" />

            <Button
                android:id="@+id/btnDialog"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_open_dialog"
                app:layout_constraintTop_toBottomOf="@id/btnWeb" />

            <Button
                android:id="@+id/btnSimpleDialog"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_open_simple_dialog"
                app:layout_constraintTop_toBottomOf="@id/btnDialog" />

            <Button
                android:id="@+id/btnSimpleListDialog"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_open_simple_list_dialog"
                app:layout_constraintTop_toBottomOf="@id/btnDialog" />

            <Button
                android:id="@+id/btnPlayer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_open_player"
                app:layout_constraintTop_toBottomOf="@id/btnSimpleDialog" />
            <Button
                android:id="@+id/btnBuildConfig"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_build_config"
                app:layout_constraintTop_toBottomOf="@id/btnPlayer" />

            <Button
                android:id="@+id/btnPermission"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_permission_storage"
                app:layout_constraintTop_toBottomOf="@id/btnBuildConfig" />
            <Button
                android:id="@+id/btnMyGameDetail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_open_game_list"
                app:layout_constraintTop_toBottomOf="@id/btnPermission" />

            <Button
                android:id="@+id/btnTestCrash"
                android:layout_width="match_parent"
                android:text="@string/debug_test_java_crash"
                app:layout_constraintTop_toBottomOf="@id/btnMyGameDetail"
                android:layout_height="wrap_content" />
            <Button
                android:id="@+id/btnTestCrashNative"
                android:layout_width="match_parent"
                android:text="@string/debug_test_native_crash"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/btnTestCrash"
                android:layout_height="wrap_content" />
            <Button
                android:id="@+id/btnTestANR"
                android:layout_width="match_parent"
                android:text="@string/debug_test_anr"
                app:layout_constraintTop_toBottomOf="@id/btnTestCrashNative"
                android:layout_height="wrap_content" />

            <Button
                android:id="@+id/btnTestFireStore"
                android:layout_width="match_parent"
                android:text="@string/debug_test_firestore"
                app:layout_constraintTop_toBottomOf="@id/btnTestANR"
                android:layout_height="wrap_content" />
            <Button
                android:id="@+id/btnTestFireStoreGet"
                android:layout_width="match_parent"
                android:text="@string/debug_test_firestore_get"
                app:layout_constraintTop_toBottomOf="@id/btnTestFireStore"
                android:layout_height="wrap_content" />

            <Button
                android:id="@+id/btnTestFacebookEvent"
                android:layout_width="match_parent"
                android:text="@string/debug_test_facebook_event"
                app:layout_constraintTop_toBottomOf="@id/btnTestFireStoreGet"
                android:layout_height="wrap_content" />

            <Button
                android:id="@+id/btnGoChatSetting"
                android:layout_width="match_parent"
                android:text="@string/debug_open_im_setting"
                app:layout_constraintTop_toBottomOf="@id/btnTestFacebookEvent"
                android:layout_height="wrap_content" />

            <Button
                android:id="@+id/btnGoBy"
                android:layout_width="match_parent"
                android:text="@string/debug_open_pay"
                app:layout_constraintTop_toBottomOf="@id/btnGoChatSetting"
                android:layout_height="wrap_content" />

            <Button
                android:id="@+id/btnSignup"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_open_register"
                app:layout_constraintTop_toBottomOf="@+id/btnLogin" />

            <Button
                android:id="@+id/btnVideo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_open_player"
                app:layout_constraintTop_toBottomOf="@+id/btnSignup" />

            <Button
                android:id="@+id/btnReplace"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_replace" />
            <Button
                android:id="@+id/btnSysShareVideo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_sys_share_video" />
            <Button
                android:id="@+id/btnProgressLoading"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/debug_loading" />

            <Button
                android:id="@+id/btnGoShare"
                android:layout_width="match_parent"
                android:text="@string/dev_share_to_friend"
                app:layout_constraintTop_toBottomOf="@id/btnProgressLoading"
                android:layout_height="wrap_content" />
            <Button
                android:id="@+id/btnGoSquare"
                android:layout_width="match_parent"
                android:text="@string/dev_go_topic_sqaure"
                app:layout_constraintTop_toBottomOf="@id/btnGoShare"
                android:layout_height="wrap_content" />
            <Button
                android:id="@+id/btnGoAiBot"
                android:layout_width="match_parent"
                android:text="@string/dev_go_aibot"
                app:layout_constraintTop_toBottomOf="@id/btnGoSquare"
                android:layout_height="wrap_content" />

            <Button
                android:id="@+id/btnFlutterTest"
                android:layout_width="match_parent"
                android:text="Flutter Test"
                app:layout_constraintTop_toBottomOf="@id/btnGoAiBot"
                android:layout_height="wrap_content" />

            <Button
                android:id="@+id/btnFlutterSettings"
                android:layout_width="match_parent"
                android:text="Flutter Settings"
                app:layout_constraintTop_toBottomOf="@id/btnFlutterTest"
                android:layout_height="wrap_content" />

        </LinearLayout>

    </ScrollView>


</androidx.constraintlayout.widget.ConstraintLayout>