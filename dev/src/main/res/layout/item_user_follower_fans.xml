<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivAvatar"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"
        android:layout_marginVertical="@dimen/dp_12"
        android:layout_marginStart="@dimen/dp_16"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/circleStyle"
        tools:src="@drawable/icon_default_avatar" />

    <com.socialplay.gpark.ui.view.FadeEditText
        android:id="@+id/tvName"
        style="@style/MetaTextView.S14.PoppinsMedium500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginTop="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_42"
        android:background="@null"
        android:enabled="false"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:singleLine="true"
        app:layout_constraintBottom_toTopOf="@id/tvId"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/ivAvatar"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" />

    <com.socialplay.gpark.ui.view.FadeEditText
        android:id="@+id/tvId"
        style="@style/MetaTextView.S12"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginTop="@dimen/dp_2"
        android:layout_marginEnd="@dimen/dp_42"
        android:layout_marginBottom="@dimen/dp_12"
        android:background="@null"
        android:enabled="false"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/color_4D4D4D"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/ivAvatar"
        app:layout_constraintTop_toBottomOf="@id/tvName"
        tools:text="ID: ddd" />

</androidx.constraintlayout.widget.ConstraintLayout>